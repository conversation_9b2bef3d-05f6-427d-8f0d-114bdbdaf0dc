'use client'

import { useState } from 'react'
import { useTranslations } from 'next-intl'
import * as Tabs from "@radix-ui/react-tabs";
import { MapPin, Car, Calendar, Users, Info, FileText } from 'lucide-react'
import { cn } from "@/lib/utils"
import { RadixBadge, RadixCard, RadixCardHeader, RadixCardTitle, RadixCardDescription, RadixCardContent } from '@/components/ui/radix-components'

import CirclesGrid from './CirclesGrid'
import EnhancedFilterBar from './EnhancedFilterBar'
import EventMap from './EventMap'
import { VenueContentDisplay } from './VenueContentDisplay'
import { RichTextTabsViewer } from '@/components/rich-text-tabs'
import { OverviewTabSkeleton, VenueTabSkeleton } from './EnhancedSkeleton'
import { createVenueFromEvent } from './types'
import { scrollToVenueMap, switchToTab } from './navigationUtils'
import type { Event } from '@/schemas/event'

interface EventDetailTabsProps {
  event: Event | null
  circles: any[]
  filteredCircles: any[]
  keyword: string
  setKeyword: (keyword: string) => void
  isLoading: boolean
}

export default function EventDetailTabs({
  event,
  circles,
  filteredCircles,
  keyword,
  setKeyword,
  isLoading
}: EventDetailTabsProps) {
  const t = useTranslations('EventDetailTabs')
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'map'>('grid')
  const [sortBy, setSortBy] = useState('name')
  const venue = createVenueFromEvent(event)

  // 通用的 Tabs.Trigger 样式 - 老式报纸风格
  const triggerClassName = "tabs-trigger-enhanced inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"

  return (
    <Tabs.Root defaultValue="exhibitors" className="w-full">
      <Tabs.List className="tabs-list-enhanced grid w-full grid-cols-4 h-10 items-center justify-center rounded-md p-1 text-stone-700 dark:text-stone-300">
        <Tabs.Trigger value="overview" className={triggerClassName}>
          <Info className="h-4 w-4" />
          {t('tabs.overview')}
        </Tabs.Trigger>
        <Tabs.Trigger value="exhibitors" className={triggerClassName}>
          <Users className="h-4 w-4" />
          {t('tabs.exhibitors')} ({filteredCircles.length})
        </Tabs.Trigger>
        <Tabs.Trigger value="content" className={triggerClassName}>
          <FileText className="h-4 w-4" />
          {t('tabs.content')}
        </Tabs.Trigger>
        <Tabs.Trigger value="venue" className={triggerClassName}>
          <MapPin className="h-4 w-4" />
          {t('tabs.venue')}
        </Tabs.Trigger>
      </Tabs.List>

      <Tabs.Content value="overview" className="space-y-6 mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
        {isLoading ? (
          <OverviewTabSkeleton />
        ) : (
          <OverviewTab event={event} circlesCount={circles.length} />
        )}
      </Tabs.Content>

      <Tabs.Content value="exhibitors" className="space-y-6 mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
        <EnhancedFilterBar
          keyword={keyword}
          setKeyword={setKeyword}
          viewMode={viewMode}
          setViewMode={setViewMode}
          sortBy={sortBy}
          setSortBy={setSortBy}
          total={filteredCircles.length}
        />

        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="loading-spinner rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
              <p className="text-muted-foreground loading-pulse">{t('exhibitors.loadingMessage')}</p>
            </div>
          </div>
        ) : (
          <CirclesGrid data={filteredCircles} />
        )}
      </Tabs.Content>

      <Tabs.Content value="content" className="space-y-6 mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
        {event?.id ? (
          <RichTextTabsViewer
            entityType="event"
            entityId={event.id}
            showLanguageSwitch={true}
          />
        ) : (
          <div className="text-center py-12">
            <p className="text-muted-foreground">{t('content.loadError')}</p>
          </div>
        )}
      </Tabs.Content>

      <Tabs.Content value="venue" className="space-y-6 mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
        {isLoading ? (
          <VenueTabSkeleton />
        ) : (
          <VenueTab event={event} venue={venue} />
        )}
      </Tabs.Content>


    </Tabs.Root>
  )
}

function OverviewTab({ event, circlesCount }: { event: Event | null; circlesCount: number }) {
  const t = useTranslations('EventDetailTabs.overview')

  if (!event) return null

  return (
    <div className="space-y-6">
      {/* 展会基本信息 */}
      <div className="grid gap-6 md:grid-cols-2">
        <RadixCard className="card-hover">
          <RadixCardHeader>
            <RadixCardTitle className="flex items-center gap-2">
              <Info className="h-5 w-5" />
              {t('eventInfo')}
            </RadixCardTitle>
          </RadixCardHeader>
          <RadixCardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">{t('date')}：</span>
                <span className="font-medium">{event.date}</span>
              </div>
              <div>
                <span className="text-muted-foreground">{t('venue')}：</span>
                <span className="font-medium">{event.venue_name}</span>
              </div>
              <div>
                <span className="text-muted-foreground">{t('exhibitors')}：</span>
                <span className="font-medium">{circlesCount}+</span>
              </div>
              <div>
                <span className="text-muted-foreground">{t('status')}：</span>
                <RadixBadge variant="secondary">{t('upcomingEvent')}</RadixBadge>
              </div>
            </div>
            {event.description && (
              <div>
                <h4 className="font-medium mb-2">{t('eventDescription')}</h4>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  {event.description}
                </p>
              </div>
            )}
          </RadixCardContent>
        </RadixCard>

        <RadixCard className="card-hover">
          <RadixCardHeader>
            <RadixCardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              {t('importantTimes')}
            </RadixCardTitle>
          </RadixCardHeader>
          <RadixCardContent className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm">{t('openingHours')}</span>
              <span className="text-sm font-medium">10:00 - 18:00</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">{t('entryDeadline')}</span>
              <span className="text-sm font-medium">17:30</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">{t('estimatedDuration')}</span>
              <span className="text-sm font-medium">3-5 {t('hours')}</span>
            </div>
          </RadixCardContent>
        </RadixCard>
      </div>

      {/* 参观须知 */}
      <RadixCard className="card-hover">
        <RadixCardHeader>
          <RadixCardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            {t('visitorGuide')}
          </RadixCardTitle>
          <RadixCardDescription>
            为了确保您有最佳的参观体验，请仔细阅读以下注意事项
          </RadixCardDescription>
        </RadixCardHeader>
        <RadixCardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-medium mb-2 text-green-600">✓ {t('allowedItems')}</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                {t.raw('allowedItemsList').map((item: string, index: number) => (
                  <li key={index}>• {item}</li>
                ))}
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2 text-red-600">✗ {t('prohibitedItems')}</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                {t.raw('prohibitedItemsList').map((item: string, index: number) => (
                  <li key={index}>• {item}</li>
                ))}
              </ul>
            </div>
          </div>
          <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
            <p className="text-sm text-blue-800">
              <strong>{t('tip')}：</strong>{t('tipContent')}
            </p>
          </div>
        </RadixCardContent>
      </RadixCard>

      {/* 快速导航 */}
      <RadixCard className="card-hover">
        <RadixCardHeader>
          <RadixCardTitle>{t('quickNavigation')}</RadixCardTitle>
          <RadixCardDescription>
            点击下方按钮快速跳转到相关信息
          </RadixCardDescription>
        </RadixCardHeader>
        <RadixCardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            <button
              onClick={() => switchToTab('exhibitors')}
              className="flex items-center gap-2 p-3 rounded-lg border border-gray-200 hover:border-primary/50 hover:bg-primary/5 transition-colors text-left"
            >
              <Users className="h-4 w-4 text-primary" />
              <div>
                <div className="font-medium text-sm">{t('exhibitors')}</div>
                <div className="text-xs text-muted-foreground">{circlesCount}+ {t('booths')}</div>
              </div>
            </button>
            <button
              onClick={scrollToVenueMap}
              className="flex items-center gap-2 p-3 rounded-lg border border-gray-200 hover:border-primary/50 hover:bg-primary/5 transition-colors text-left"
            >
              <MapPin className="h-4 w-4 text-primary" />
              <div>
                <div className="font-medium text-sm">{t('venueMap')}</div>
                <div className="text-xs text-muted-foreground">{t('locationGuide')}</div>
              </div>
            </button>
            <button
              onClick={() => switchToTab('venue')}
              className="flex items-center gap-2 p-3 rounded-lg border border-gray-200 hover:border-primary/50 hover:bg-primary/5 transition-colors text-left"
            >
              <Car className="h-4 w-4 text-primary" />
              <div>
                <div className="font-medium text-sm">{t('transportGuide')}</div>
                <div className="text-xs text-muted-foreground">{t('transportGuide')}</div>
              </div>
            </button>
          </div>
        </RadixCardContent>
      </RadixCard>
    </div>
  )
}

function VenueTab({ event, venue }: { event: Event | null; venue: any }) {
  const t = useTranslations('EventDetailTabs.venue')

  if (!event) return null

  return (
    <div className="space-y-6">
      {/* 会场基本信息 */}
      <div className="grid gap-6 md:grid-cols-2">
        <RadixCard className="card-hover">
          <RadixCardHeader>
            <RadixCardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              {t('venueDetails')}
            </RadixCardTitle>
          </RadixCardHeader>
          <RadixCardContent className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">{t('venueName')}</h4>
              <p className="text-sm text-muted-foreground">{event.venue_name}</p>
            </div>
            {event.venue_address && (
              <div>
                <h4 className="font-medium mb-2">{t('detailedAddress')}</h4>
                <p className="text-sm text-muted-foreground">{event.venue_address}</p>
              </div>
            )}
            <div>
              <h4 className="font-medium mb-2">{t('openingHours')}</h4>
              <p className="text-sm text-muted-foreground">10:00 - 18:00</p>
            </div>
            <div>
              <h4 className="font-medium mb-2">{t('entranceFee')}</h4>
              <p className="text-sm text-muted-foreground">{t('freeEntry')}</p>
            </div>
          </RadixCardContent>
        </RadixCard>

        <RadixCard className="card-hover">
          <RadixCardHeader>
            <RadixCardTitle className="flex items-center gap-2">
              <Car className="h-5 w-5" />
              {t('transportGuide')}
            </RadixCardTitle>
          </RadixCardHeader>
          <RadixCardContent className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">{t('publicTransport')}</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                {t.raw('transportOptions').map((option: string, index: number) => (
                  <li key={index}>• {option}</li>
                ))}
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">{t('driving')}</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                {t.raw('drivingInfo').map((info: string, index: number) => (
                  <li key={index}>• {info}</li>
                ))}
              </ul>
            </div>
          </RadixCardContent>
        </RadixCard>
      </div>

      {/* 详细地图 */}
      <RadixCard id="venue-map-section" className="card-hover">
        <RadixCardHeader>
          <RadixCardTitle>{t('detailedMap')}</RadixCardTitle>
          <RadixCardDescription>
            {t('mapDescription')}
          </RadixCardDescription>
        </RadixCardHeader>
        <RadixCardContent>
          <EventMap
            venue={venue}
            className="h-[500px] rounded-lg overflow-hidden"
            isPreview={false}
          />
        </RadixCardContent>
      </RadixCard>

      {/* 场馆详细信息 */}
      {venue?.id && (
        <VenueContentDisplay
          venueId={venue.id}
          className="w-full"
        />
      )}
    </div>
  )
}