/* eslint-disable @next/next/no-img-element */
import { expect, test, vi } from "vitest"

import EnhancedEventHeader from "@/components/events/EnhancedEventHeader"
import { renderWithProviders, screen } from "@test/test-utils"

// Mock 子组件
vi.mock("@/components/events/EventPoster", () => {
  return {
    __esModule: true,
    default: ({ eventName }: { eventName?: string | null }) => (
      <div data-testid="event-poster">
        {eventName && <img alt={`${eventName} - Event Poster`} />}
      </div>
    ),
  }
})

vi.mock("@/components/events/EventMap", () => {
  return {
    __esModule: true,
    default: () => <div data-testid="event-map" />,
  }
})

const sampleEvent = {
  id: "reitaisai-22",
  name: "Reitaisai 22",
  date: "May 3, 2025 (Sat) 10:30 – 15:30",
  date_sort: 20250503,
  venue_name: "Tokyo Big Sight",
  venue_address: "3-11-1 Ariake, Koto City, Tokyo",
  venue_lat: 35.6298,
  venue_lng: 139.793,
  url: "https://reitaisai.com/rts22/",
  image_url: "/images/events/reitaisai-22/thumb.jpg",
  description: "东方Project同人展会",
} as any

test("renders enhanced event header with hero section", () => {
  renderWithProviders(<EnhancedEventHeader event={sampleEvent} circlesCount={150} />)

  // 检查英雄区域元素
  expect(screen.getByRole("heading", { name: /Reitaisai 22/ })).toBeInTheDocument()
  expect(screen.getByText("May 3, 2025 (Sat) 10:30 – 15:30 • Tokyo Big Sight")).toBeInTheDocument()
  expect(screen.getByText("东方Project同人展会")).toBeInTheDocument()

  // 检查状态徽章
  expect(screen.getByText("即将举办")).toBeInTheDocument()

  // 检查官方网站按钮
  const officialButton = screen.getByRole("link", { name: /官方网站/ })
  expect(officialButton).toHaveAttribute("href", "https://reitaisai.com/rts22/")
  expect(officialButton).toHaveAttribute("target", "_blank")
})

test("renders quick info cards", () => {
  renderWithProviders(<EnhancedEventHeader event={sampleEvent} circlesCount={150} />)

  // 检查快速信息卡片
  expect(screen.getByText("May 3, 2025 (Sat) 10:30 – 15:30")).toBeInTheDocument()
  expect(screen.getByText("Tokyo Big Sight")).toBeInTheDocument()
  expect(screen.getByText("150+")).toBeInTheDocument()
  expect(screen.getByText("10:00 - 18:00")).toBeInTheDocument()
})

test("renders venue information in info cards", () => {
  renderWithProviders(<EnhancedEventHeader event={sampleEvent} circlesCount={150} />)

  // 检查场馆信息在快速信息卡片中显示
  expect(screen.getByText("Tokyo Big Sight")).toBeInTheDocument()
  expect(screen.getByText("点击查看地图")).toBeInTheDocument()
})

test("renders skeleton when event is null", () => {
  renderWithProviders(<EnhancedEventHeader event={null} circlesCount={0} />)

  // 应该显示骨架屏
  const skeletonElements = document.querySelectorAll('.animate-pulse')
  expect(skeletonElements.length).toBeGreaterThan(0)
})

test("does not render official website button when url is missing", () => {
  const eventWithoutUrl = { ...sampleEvent, url: undefined }
  renderWithProviders(<EnhancedEventHeader event={eventWithoutUrl} circlesCount={150} />)

  expect(screen.queryByRole("link", { name: /官方网站/ })).not.toBeInTheDocument()
})

test("handles missing optional fields gracefully", () => {
  const minimalEvent = {
    id: "test-event",
    name: "Test Event",
    date: "2025-01-01",
    venue_name: "Test Venue",
    venue_lat: 35.6298,
    venue_lng: 139.793,
  } as any

  renderWithProviders(<EnhancedEventHeader event={minimalEvent} circlesCount={0} />)

  expect(screen.getByRole("heading", { name: /Test Event/ })).toBeInTheDocument()
  expect(screen.getByText("2025-01-01 • Test Venue")).toBeInTheDocument()
  expect(screen.queryByText("东方Project同人展会")).not.toBeInTheDocument()
})

test("applies correct CSS classes for styling", () => {
  const { container } = renderWithProviders(<EnhancedEventHeader event={sampleEvent} circlesCount={150} />)

  // 检查关键的 CSS 类
  expect(container.querySelector('.hero-gradient')).toBeInTheDocument()
  // 检查快速信息卡片区域
  expect(container.querySelector('header')).toBeInTheDocument()
})

test("renders with loading state", () => {
  renderWithProviders(<EnhancedEventHeader event={sampleEvent} isLoading={true} circlesCount={150} />)

  // 组件应该正常渲染，加载状态不影响基本信息显示
  expect(screen.getByRole("heading", { name: /Reitaisai 22/ })).toBeInTheDocument()
  expect(screen.getByText("Tokyo Big Sight")).toBeInTheDocument()
})

test("renders with error state", () => {
  renderWithProviders(<EnhancedEventHeader event={sampleEvent} error="Map loading failed" circlesCount={150} />)

  // 组件应该正常渲染，错误状态不影响基本信息显示
  expect(screen.getByRole("heading", { name: /Reitaisai 22/ })).toBeInTheDocument()
  expect(screen.getByText("Tokyo Big Sight")).toBeInTheDocument()
})
