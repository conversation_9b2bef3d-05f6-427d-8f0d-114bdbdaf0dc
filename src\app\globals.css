@import "tailwindcss";
@import "tw-animate-css";
@import "leaflet/dist/leaflet.css";
@import "../styles/event-detail-enhancements.css";
@import "../styles/rich-text-editor.css";

/* 3D 变换和动画工具类 */
.perspective-1000 {
  perspective: 1000px;
}

.transform-gpu {
  transform: translateZ(0);
  will-change: transform;
}

.backface-hidden {
  backface-visibility: hidden;
}

/* 文本截断工具类 */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
}

/* 射命丸文主题装饰元素 */
/* 风的动画效果 */
@keyframes wind-flow {
  0%, 100% {
    transform: translateX(0) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateX(20px) rotate(5deg);
    opacity: 0.6;
  }
}

@keyframes wind-flow-reverse {
  0%, 100% {
    transform: translateX(0) rotate(0deg);
    opacity: 0.2;
  }
  50% {
    transform: translateX(-15px) rotate(-3deg);
    opacity: 0.5;
  }
}

.wind-decoration {
  position: absolute;
  width: 100px;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--primary), transparent);
  animation: wind-flow 3s ease-in-out infinite;
  pointer-events: none;
}

.wind-decoration-reverse {
  position: absolute;
  width: 80px;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--accent), transparent);
  animation: wind-flow-reverse 4s ease-in-out infinite;
  pointer-events: none;
}

/* 新闻纸纹理背景 */
.newspaper-texture {
  background-image:
    linear-gradient(90deg, var(--border) 1px, transparent 1px),
    linear-gradient(var(--border) 1px, transparent 1px);
  background-size: 20px 20px;
  opacity: 0.3;
}

/* 老式报纸纹理背景 - 简化版 */
.vintage-paper-texture {
  background-image:
    radial-gradient(circle at 20% 50%, rgba(120, 119, 108, 0.015) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(120, 119, 108, 0.015) 0%, transparent 50%),
    linear-gradient(90deg, rgba(120, 119, 108, 0.005) 1px, transparent 1px),
    linear-gradient(rgba(120, 119, 108, 0.005) 1px, transparent 1px);
  background-size: 300px 300px, 280px 280px, 20px 20px, 20px 20px;
  background-position: 0 0, 150px 150px, 0 0, 0 0;
}



/* 新闻主题渐变背景 - 老式报纸风格 */
.news-gradient-bg {
  background: linear-gradient(135deg,
    #F9F7F1 0%,
    #F6F3EB 50%,
    #F3EFE5 100%
  );
}



/* 深色模式变体已移除 */

@theme inline {
  /* 颜色系统 - 老式报纸主题 */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);

  /* 图表颜色 */
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);

  /* 侧边栏颜色 */
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  /* 字体 */
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* 圆角 */
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;

  /* 射命丸文新闻主题色彩系统 */
  /* 主色 - 新闻红 */
  --primary: #DC2626;                       /* 专业新闻红 */
  --primary-hover: #B91C1C;                 /* 悬停状态 */
  --primary-foreground: #FEFEFE;            /* 白色文字 */

  /* 辅助色 - 天狗黑 */
  --secondary: #262626;                     /* 深黑色 */
  --secondary-hover: #404040;               /* 悬停状态 */
  --secondary-foreground: #FEFEFE;          /* 白色文字 */

  /* 强调色 - 金色 */
  --accent: #F59E0B;                        /* 金色强调 */
  --accent-hover: #D97706;                  /* 悬停状态 */
  --accent-foreground: #262626;             /* 黑色文字 */

  /* 背景和表面 - 老式报纸风格 */
  --background: #F9F7F1;                    /* 温暖米黄色背景 */
  --foreground: #262626;                    /* 深黑文字 */
  --card: #FAF8F3;                          /* 卡片背景 */
  --card-foreground: #262626;               /* 卡片文字 */
  --popover: #FAF8F3;                       /* 弹窗背景 */
  --popover-foreground: #262626;            /* 弹窗文字 */

  /* 中性色 - 老式报纸风格 */
  --muted: #F5F2EA;                         /* 浅米黄背景 */
  --muted-foreground: #737373;              /* 中等灰色文字 */

  /* 语义色 */
  --destructive: #DC2626;                   /* 错误红 */
  --success: #16A34A;                       /* 成功绿 */
  --warning: #F59E0B;                       /* 警告橙 */
  --info: #3B82F6;                          /* 信息蓝 */

  /* 边框和输入 */
  --border: #EEEEEE;                        /* 边框色 */
  --input: #EEEEEE;                         /* 输入框边框 */
  --ring: #DC2626;                          /* 焦点环 */

  /* 图表色彩 - 新闻主题 */
  --chart-1: #DC2626;                       /* 新闻红 */
  --chart-2: #F59E0B;                       /* 金色 */
  --chart-3: #262626;                       /* 深黑 */
  --chart-4: #737373;                       /* 中灰 */
  --chart-5: #D1D5DB;                       /* 浅灰 */

  /* 侧边栏 - 老式报纸风格 */
  --sidebar: #F7F5F1;                       /* 米黄色侧边栏 */
  --sidebar-foreground: #262626;            /* 深色文字 */
  --sidebar-primary: #DC2626;               /* 新闻红 */
  --sidebar-primary-foreground: #FEFEFE;    /* 白色文字 */
  --sidebar-accent: #F3F0E8;                /* 深米黄强调 */
  --sidebar-accent-foreground: #262626;     /* 深色文字 */
  --sidebar-border: #E8E4DC;                /* 米黄边框色 */
  --sidebar-ring: #DC2626;                  /* 焦点环 */
}

/* 深色模式已移除 - 仅保留老式报纸浅色主题 */

/* 管理员页面专用样式 - 简洁黑色主题 */
.admin-form {
  /* 覆盖输入框焦点环颜色为黑色 */
  --ring: #262626;
}

.admin-form input:focus-visible {
  --tw-ring-color: #262626;
  box-shadow: 0 0 0 2px var(--tw-ring-offset-color), 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
}

.admin-form textarea:focus-visible {
  --tw-ring-color: #262626;
  box-shadow: 0 0 0 2px var(--tw-ring-offset-color), 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
}

/* 管理员页面标签页样式覆盖 */
.admin-form .tabs-list {
  background-color: #f1f5f9;
}

.admin-form .tabs-trigger {
  color: #64748b;
}

.admin-form .tabs-trigger[data-state="active"] {
  background-color: #262626;
  color: white;
}

/* 管理员页面按钮保持红色 */
.admin-form button[type="submit"] {
  background-color: #DC2626;
  color: white;
}

.admin-form button[type="submit"]:hover {
  background-color: #B91C1C;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* 修复 Leaflet 地图 z-index 问题，确保不会覆盖导航栏 */
@layer components {
  .leaflet-container {
    z-index: 1 !important;
  }

  .leaflet-control-container {
    z-index: 10 !important;
  }

  .leaflet-popup-pane {
    z-index: 20 !important;
  }

  .leaflet-tooltip-pane {
    z-index: 25 !important;
  }

  .leaflet-marker-pane {
    z-index: 30 !important;
  }
}

/* 确保老式报纸背景色应用到整个页面 */
html {
  background-color: #F9F7F1 !important; /* 直接使用米黄色 */
  color: #262626;
}

body {
  background-color: #F9F7F1 !important; /* 直接使用米黄色 */
  color: #262626;
  min-height: 100vh;
}

/* 也确保 Next.js 的根元素有正确的背景 */
#__next {
  background-color: #F9F7F1 !important;
  min-height: 100vh;
}
