/* Event Detail Page Visual Enhancements */

/* 英雄区域渐变背景 - 老式报纸风格 */
.hero-gradient {
  background: linear-gradient(135deg,
    #F9F7F1 0%,
    #F6F3EB 50%,
    #F3EFE5 100%);
}

/* 卡片悬停效果 */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 
              0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 信息卡片动画 - 老式报纸风格 */
.info-card {
  transition: all 0.2s ease-in-out;
  border: 1px solid #D4C4A8;
  background: #FEFCF8;
  box-shadow: 0 1px 3px rgba(139, 117, 93, 0.1);
}

.info-card:hover {
  border-color: #A0522D;
  background: #FDF9F3;
  backdrop-filter: blur(8px);
  box-shadow: 0 4px 12px rgba(139, 117, 93, 0.15);
}

/* 标签页导航增强 - 老式报纸风格 */
.tabs-list-enhanced {
  background: #F6F3EB;
  backdrop-filter: blur(8px);
  border: 1px solid #D4C4A8;
}

.tabs-trigger-enhanced {
  transition: all 0.2s ease-in-out;
  border-radius: 6px;
  margin: 2px;
  border: 1px solid transparent;
}

.tabs-trigger-enhanced[data-state="active"] {
  background: #8B4513;
  color: #FFFFFF;
  border-color: #654321;
  box-shadow: 0 2px 4px rgba(139, 69, 19, 0.2);
}

/* 搜索框增强 - 老式报纸风格 */
.search-input-enhanced {
  transition: all 0.2s ease-in-out;
  border: 1px solid #D4C4A8;
  background: #FEFCF8;
}

.search-input-enhanced:focus {
  border-color: #DC2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
  background: #FFFFFF;
}

/* 筛选标签动画 - 老式报纸风格 */
.filter-badge {
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  border: 1px solid #D4C4A8;
  background: #FEFCF8;
}

.filter-badge:hover {
  transform: scale(1.05);
  border-color: #A0522D;
  background: #F6F3EB;
}

.filter-badge.active {
  background: #DC2626;
  color: #FFFFFF;
  border-color: #B91C1C;
  box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
}

/* 加载动画增强 */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 骨架屏动画 - 老式报纸风格 */
.skeleton {
  background: linear-gradient(90deg,
    #F0EBD8 25%,
    #F6F3EB 50%,
    #F0EBD8 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 按钮增强效果 - 老式报纸风格 */
.button-enhanced {
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
  background: #A0522D;
  border: 1px solid #8B4513;
  color: #FFFFFF;
}

.button-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent);
  transition: left 0.5s;
}

.button-enhanced:hover {
  background: #8B4513;
  border-color: #654321;
  box-shadow: 0 4px 12px rgba(139, 69, 19, 0.2);
}

.button-enhanced:hover::before {
  left: 100%;
}

/* 官方网站按钮 - 强红色 */
.button-website {
  background: #DC2626 !important;
  border-color: #B91C1C !important;
  color: #FFFFFF !important;
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.button-website::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent);
  transition: left 0.5s;
}

.button-website:hover {
  background: #B91C1C !important;
  border-color: #991B1B !important;
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
  transform: translateY(-1px);
}

.button-website:hover::before {
  left: 100%;
}

/* 地图容器增强 */
.map-container {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 
              0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.3s ease-in-out;
}

.map-container:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 
              0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 响应式增强 */
@media (max-width: 768px) {
  .hero-gradient {
    background: linear-gradient(180deg,
      #F9F7F1 0%,
      #F6F3EB 100%);
  }

  .card-hover:hover {
    transform: none;
  }

  .info-card {
    padding: 12px;
  }
}



/* 无障碍增强 */
@media (prefers-reduced-motion: reduce) {
  .card-hover,
  .info-card,
  .filter-badge,
  .button-enhanced {
    transition: none;
  }
  
  .loading-spinner,
  .loading-pulse,
  .skeleton {
    animation: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .info-card {
    border-width: 2px;
  }
  
  .filter-badge.active {
    border: 2px solid hsl(var(--primary));
  }
  
  .search-input-enhanced:focus {
    border-width: 2px;
  }
}

/* 地图容器样式 */
.map-container {
  @apply relative overflow-hidden rounded-lg border border-gray-200;
}

/* 地图预览容器样式 */
.map-preview-container {
  @apply relative overflow-hidden rounded-lg border border-gray-200 transition-colors;
}

.map-preview-container:hover {
  @apply border-primary/50;
}
